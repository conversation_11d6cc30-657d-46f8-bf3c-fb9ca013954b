#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试IP切换脚本 - 用于验证GUI实时输出功能
"""

import time
import sys

def log(msg: str, level="INFO"):
    """模拟日志输出"""
    timestamp = time.strftime('%H:%M:%S')
    prefix = f"[{timestamp}] [{level}]"
    print(f"{prefix} {msg}")
    sys.stdout.flush()  # 强制刷新输出缓冲区

def main():
    """主测试函数"""
    log("启动IP切换脚本 - 测试版")
    log("当前API: TEST")
    log("代理名称: 测试代理")
    log("调试模式: 开启")
    
    try:
        # 模拟获取当前IP
        log("正在获取当前IP...")
        time.sleep(1)
        old_ip = "*************"
        log(f"当前 IP: {old_ip}")
        
        # 模拟切换到直连模式
        log("关闭代理 (Clash→DIRECT)…")
        time.sleep(0.5)
        
        # 模拟调用切换IP接口
        log("准备调用IP切换接口...")
        log("第 1/3 次尝试切换IP...")
        log("调用切换IP接口 - 预计等待时间: 最长 30s")
        
        # 模拟API调用过程
        for i in range(5):
            log(f"正在调用API... ({i+1}/5)")
            time.sleep(1)
        
        new_ip = "*************"
        log(f"切换IP接口返回成功 - 耗时: 5.2s - 返回IP: {new_ip}")
        log(f"✓ IP切换成功: {old_ip} → {new_ip}")
        
        # 模拟等待生效
        log("等待 3s 让IP切换生效...")
        time.sleep(3)
        
        # 模拟轮询检查
        log("开始轮询检查新IP...")
        for i in range(3):
            log(f"第{i+1}/3 次轮询: 检测到新IP {new_ip}")
            time.sleep(1)
        
        # 模拟恢复代理模式
        log("刷新 Clash 连接 & DNS...")
        time.sleep(1)
        log("✓ IP切换流程完成")
        
        # 模拟最终验证
        log("等0秒后开始最终验证")
        log("开始最终验证步骤...")
        time.sleep(2)
        log("🎉 完整的IP切换流程成功完成！代理模式下IP已更新")
        
        # 模拟诊断报告
        log("="*50)
        log("诊断报告")
        log("="*50)
        log("总日志条数: 15")
        log("错误日志: 0")
        log("警告日志: 0")
        log("总耗时: 15.2s")
        log("="*50)
        
        log("✓ 测试脚本执行完成")
        
    except KeyboardInterrupt:
        log("用户中断操作", "WARN")
        sys.exit(1)
    except Exception as e:
        log(f"脚本执行异常: {e}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
