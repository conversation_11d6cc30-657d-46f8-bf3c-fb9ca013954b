#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
家宽 IP 自动切换 · 增强诊断版
----------------------------------------------------------------
1. 可选关闭 Clash 代理（DIRECT）
2. 调 changeip，打印原始返回（支持长时间等待）
3. 轮询 showip，直到新 IP
4. 清连接+刷新 DNS，打印每一步结果
5. 增强诊断：请求时间统计、详细错误信息、切换历史记录
6. 强制代理验证：确保代理模式下IP真正切换生效
----------------------------------------------------------------
重要配置：
请确认您的 Clash 代理端口设置：
- HTTP代理端口: 默认7890 (请在配置区域修改 CLASH_HTTP_PROXY_PORT)
- SOCKS代理端口: 默认7891 (请在配置区域修改 CLASH_SOCKS_PROXY_PORT)
- 外部控制端口: 默认9097 (请在配置区域修改 CLASH_PORT)

查看方法：Clash → 设置 → 网络 → 端口设置
----------------------------------------------------------------
用法：
  python switch_ip.py           # 正常流程
  python switch_ip.py --dry-run # 只测接口，不改 Clash
  python switch_ip.py --debug   # 详细调试模式
"""
import argparse, os, sys, time, json, re
import requests
import ipaddress
from datetime import datetime, timedelta

# ======== 用户可修改区域 =====================================
# API配置 - 可以选择使用不同的API服务商
# 选项1: pqs.pw API (纯文本响应)
PQS_SHOW_URL   = "https://api.pqs.pw/show/j8pe8vuffh"
PQS_CHANGE_URL = "https://api.pqs.pw/ipch/j8pe8vuffh"

# 选项2: lisahost.com API (JSON响应)
LISA_SHOW_URL   = "https://lisahost.com/hinet.php?a=showip&id=89339&pass=Aa123123.."
LISA_CHANGE_URL = "https://lisahost.com/hinet.php?a=changeip&id=89339&pass=Aa123123.."

# 当前使用的API (修改这里来切换API)
# 可选值: "pqs" 或 "lisa"
CURRENT_API = "pqs"

# 代理名称配置 (修改这里来匹配您的Clash配置中的代理名称)
# 这个名称必须与您的Clash配置文件中的代理名称完全一致
PQS_PROXY_NAME = "pqs555"      # pqs.pw API对应的代理名称
LISA_PROXY_NAME = "c台湾h2"   # lisahost.com API对应的代理名称

# 根据选择设置实际使用的URL和代理名称
if CURRENT_API == "lisa":
    SHOW_URL = LISA_SHOW_URL
    CHANGE_URL = LISA_CHANGE_URL
    PROXY_NAME = LISA_PROXY_NAME
else:
    SHOW_URL = PQS_SHOW_URL
    CHANGE_URL = PQS_CHANGE_URL
    PROXY_NAME = PQS_PROXY_NAME

CLASH_HOST  = "127.0.0.1"
CLASH_PORT  = 9097      # Verge → 设置 → Clash 设置 → 外部控制端口
CLASH_TOKEN = "1"       # 如果 config.yml 有 secret，把值填这里

# Clash 代理端口配置（用于强制代理验证）
CLASH_HTTP_PROXY_PORT = 7899   # Clash HTTP代理端口，请根据您的配置修改
CLASH_SOCKS_PROXY_PORT = 7898  # Clash SOCKS代理端口（备用）

# 快速IP替换配置
CLASH_CONFIG_FILE = r"C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\profiles\RWaNqKYU2Mtk.yaml"
ENABLE_FAST_IP_REPLACEMENT = True  # 是否启用快速IP替换功能

CHANGEIP_TIMEOUT = 180  # changeip 请求超时时间（3分钟）
SHOWIP_TIMEOUT   = 30   # showip 请求超时时间
PROPAGATE_WAIT   = 1   # 切 IP 后先等多少秒再开始轮询（增加到5秒）
POLL_INTERVAL    = 5    # 轮询间隔（增加到5秒）
MAX_POLL         = 15   # 最多轮询次数（总等待 2 分钟）

# 切换冷却时间（避免频繁切换）
MIN_SWITCH_INTERVAL = 300  # 5分钟内不重复切换

# IP切换重试配置
MAX_IP_CHANGE_RETRIES = 10  # 当API返回相同IP时的最大重试次数
IP_CHANGE_RETRY_INTERVAL = 10  # 重试间隔（秒）

# 公网IP检测接口（用于最终验证代理效果）
PUBLIC_IP_APIS = [
    "https://api.ipify.org",
    "https://ipv4.icanhazip.com", 
    "https://ifconfig.me",
    "https://4.ipw.cn"
]
PROXY_TEST_TIMEOUT = 3  # 代理测试超时时间
MAX_PROXY_RETRY = 10    # 代理验证最大重试次数
PROXY_RETRY_INTERVAL = 2  # 代理验证重试间隔
# ============================================================

session = requests.Session()
session.trust_env = False         # 避免继承系统代理
session.headers.update({"User-Agent": "python-switch-ip/2.0"})

# 全局变量用于诊断
last_switch_time = None
switch_history = []
debug_mode = False

def log(msg: str, level="INFO"):
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]  # 毫秒精度
    prefix = f"[{timestamp}] [{level}]"
    print(f"{prefix} {msg}")
    
    # 保存到历史记录
    switch_history.append({
        "time": datetime.now(),
        "level": level,
        "message": msg
    })

def debug_log(msg: str):
    if debug_mode:
        log(msg, "DEBUG")

def is_valid_ip(ip_str):
    """验证IP地址格式是否有效"""
    if not ip_str or not isinstance(ip_str, str):
        return False

    # 去除首尾空白字符
    ip_str = ip_str.strip()

    # 检查是否包含无效字符（如字母、特殊符号等）
    if any(char.isalpha() for char in ip_str):
        return False

    try:
        # 使用ipaddress模块验证IP地址
        ipaddress.IPv4Address(ip_str)
        return True
    except (ipaddress.AddressValueError, ValueError):
        return False


def clash_api(path, method="GET", **kw):
    url = f"http://{CLASH_HOST}:{CLASH_PORT}{path}"
    headers = kw.pop("headers", {})
    if CLASH_TOKEN:
        headers["Authorization"] = f"Bearer {CLASH_TOKEN}"
    
    start_time = time.time()
    try:
        r = session.request(method, url, headers=headers, timeout=10, **kw)
        r.raise_for_status()
        elapsed = time.time() - start_time
        debug_log(f"Clash API {method} {path} - 耗时: {elapsed:.2f}s")
        return r.json() if r.headers.get("content-type", "").startswith("application/json") else r.text
    except Exception as e:
        elapsed = time.time() - start_time
        log(f"Clash API 请求失败 {method} {path} - 耗时: {elapsed:.2f}s - 错误: {e}", "ERROR")
        raise

def set_clash_mode(mode: str):
    clash_api("/configs", "PATCH", json={"mode": mode.lower()})
    log(f"Clash 模式已切换为 {mode.upper()}")

def flush_dns_and_conn():
    clash_api("/connections", "DELETE")
    clash_api("/configs", "PATCH", json={"dns": {"flush": True}})
    log("已清连接 & 刷新 DNS")

def fast_replace_proxy_ip(new_ip):
    """快速替换代理配置中的IP地址"""
    if not ENABLE_FAST_IP_REPLACEMENT:
        debug_log("快速IP替换功能已禁用")
        return False

    if not os.path.exists(CLASH_CONFIG_FILE):
        log(f"配置文件不存在: {CLASH_CONFIG_FILE}", "WARN")
        return False
        
    # 验证新IP地址格式
    if not is_valid_ip(new_ip):
        log(f"快速IP替换失败: 无效的IP地址格式 '{new_ip}'", "ERROR")
        return False

    try:
        log(f"开始快速替换代理IP为: {new_ip}")

        # 1. 备份配置文件
        backup_file = CLASH_CONFIG_FILE + f".backup.{int(time.time())}"
        with open(CLASH_CONFIG_FILE, 'r', encoding='utf-8') as src:
            original_content = src.read()
        with open(backup_file, 'w', encoding='utf-8') as dst:
            dst.write(original_content)
        debug_log(f"已备份配置文件到: {backup_file}")

        # 2. 替换指定代理的服务器IP
        pattern = rf'(- name: {re.escape(PROXY_NAME)}\s*\n(?:[^\n]*\n)*?\s*server:\s*)([^\s\n]+)'

        def replace_server(match):
            prefix = match.group(1)
            old_server = match.group(2)
            debug_log(f"替换 {PROXY_NAME} 服务器: {old_server} → {new_ip}")
            return prefix + new_ip

        new_content = re.sub(pattern, replace_server, original_content, flags=re.MULTILINE)

        if new_content == original_content:
            log(f"未找到 {PROXY_NAME} 代理配置，跳过快速替换", "WARN")
            return False

        # 3. 写入新配置
        with open(CLASH_CONFIG_FILE, 'w', encoding='utf-8') as f:
            f.write(new_content)
        debug_log("新配置已写入文件")

        # 4. 重新加载 Clash 配置
        result = clash_api("/configs", "PUT", json={"path": CLASH_CONFIG_FILE})
        if result is not None:
            log("✓ 快速IP替换成功，Clash配置已重载")

            # 5. 等待配置生效并测试连通性
            time.sleep(2)
            proxies = clash_api("/proxies")
            if proxies and PROXY_NAME in proxies.get("proxies", {}):
                proxy_info = proxies["proxies"][PROXY_NAME]
                is_alive = proxy_info.get('alive', False)
                debug_log(f"{PROXY_NAME} 代理状态: {'在线' if is_alive else '离线'}")

                if is_alive:
                    history = proxy_info.get("history", [])
                    if history:
                        delay = history[-1].get("delay", 0)
                        log(f"✓ 代理连通性测试通过，延迟: {delay}ms")
                    return True

            log("代理连通性测试失败，但配置已更新", "WARN")
            return True
        else:
            log("Clash配置重载失败", "ERROR")
            # 恢复备份
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_content = f.read()
            with open(CLASH_CONFIG_FILE, 'w', encoding='utf-8') as f:
                f.write(backup_content)
            debug_log("已恢复原配置")
            return False

    except Exception as e:
        log(f"快速IP替换失败: {e}", "ERROR")
        return False

def api_request(url, timeout, description):
    """通用API请求函数，带详细诊断，支持JSON和纯文本响应"""
    start_time = time.time()
    log(f"开始 {description} 请求 - 超时: {timeout}s")
    debug_log(f"请求URL: {url}")

    try:
        r = session.get(url, timeout=timeout)
        elapsed = time.time() - start_time
        log(f"{description} 请求完成 - 耗时: {elapsed:.2f}s - 状态码: {r.status_code}")

        r.raise_for_status()

        # 尝试解析JSON，如果失败则返回纯文本
        try:
            result = r.json()
            debug_log(f"{description} JSON响应: {result}")
            return result, elapsed
        except json.JSONDecodeError:
            # 纯文本响应，去除首尾空白字符
            result = r.text.strip()
            debug_log(f"{description} 文本响应: {result}")
            return result, elapsed

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        log(f"{description} 请求超时 - 耗时: {elapsed:.2f}s", "ERROR")
        raise
    except requests.exceptions.RequestException as e:
        elapsed = time.time() - start_time
        log(f"{description} 请求异常 - 耗时: {elapsed:.2f}s - 错误: {e}", "ERROR")
        raise

def get_current_ip():
    """获取当前IP"""
    result, elapsed = api_request(SHOW_URL, SHOWIP_TIMEOUT, "获取IP")

    # 处理不同的响应格式
    if isinstance(result, dict):
        # JSON格式响应（旧API格式）
        if result.get("status") != "success":
            error_msg = result.get("msg", "未知错误")
            raise RuntimeError(f"获取 IP 失败: {error_msg}")
        ip = result["ip"]
    else:
        # 纯文本格式响应（新API格式）
        ip = result.strip()
        # 严格验证IP格式
        if not is_valid_ip(ip):
            raise RuntimeError(f"获取 IP 失败: 无效的IP格式 '{ip}'")

    log(f"当前 IP: {ip}")
    return ip

def change_ip(skip_cooldown=False):
    """切换IP

    Args:
        skip_cooldown: 是否跳过冷却时间检查（用于重试场景）
    """
    global last_switch_time

    # 检查切换频率限制
    now = datetime.now()
    if not skip_cooldown and last_switch_time:
        time_since_last = (now - last_switch_time).total_seconds()
        if time_since_last < MIN_SWITCH_INTERVAL:
            wait_time = MIN_SWITCH_INTERVAL - time_since_last
            log(f"距离上次切换仅 {time_since_last:.1f}s，需等待 {wait_time:.1f}s 后再切换", "WARN")
            time.sleep(wait_time)
    
    log(f"调用切换IP接口 - 预计等待时间: 最长 {CHANGEIP_TIMEOUT}s")
    result, elapsed = api_request(CHANGE_URL, CHANGEIP_TIMEOUT, "切换IP")

    # 处理不同的响应格式
    if isinstance(result, dict):
        # JSON格式响应（旧API格式）
        if result.get("status") != "success":
            error_msg = result.get("msg", "未知错误")
            raise RuntimeError(f"切换 IP 失败: {error_msg}")
        returned_ip = result.get("ip", "未返回IP")
    else:
        # 纯文本格式响应（新API格式）
        returned_ip = result.strip()
        # 严格验证IP格式
        if not is_valid_ip(returned_ip):
            raise RuntimeError(f"切换 IP 失败: 无效的IP格式 '{returned_ip}'")

    log(f"切换IP接口返回成功 - 耗时: {elapsed:.2f}s - 返回IP: {returned_ip}")

    last_switch_time = now
    return returned_ip

def check_ip_change_effectiveness(old_ip, expected_new_ip=None):
    """检查IP切换的有效性"""
    log("开始检查IP切换有效性...")
    
    # 获取多个来源的IP进行交叉验证
    ip_sources = [
        ("本服务", SHOW_URL),
        # 可以添加其他IP检测服务进行交叉验证
    ]
    
    current_ips = {}
    for source_name, url in ip_sources:
        try:
            if url == SHOW_URL:
                ip = get_current_ip()
            else:
                # 其他IP检测服务的处理逻辑
                pass
            current_ips[source_name] = ip
            debug_log(f"{source_name} 检测到IP: {ip}")
        except Exception as e:
            log(f"{source_name} IP检测失败: {e}", "WARN")
    
    if not current_ips:
        log("所有IP检测源都失败", "ERROR")
        return False
    
    # 分析结果
    unique_ips = set(current_ips.values())
    if len(unique_ips) == 1:
        new_ip = list(unique_ips)[0]
        if new_ip != old_ip:
            log(f"✓ IP切换成功: {old_ip} → {new_ip}")
            return True
        else:
            log(f"✗ IP未改变: 仍为 {old_ip}", "WARN")
            return False
    else:
        log(f"多个IP检测源结果不一致: {current_ips}", "WARN")
        return False

def check_proxy_port_available():
    """检测Clash代理端口是否可用"""
    import socket
    
    ports_to_check = [
        (CLASH_HTTP_PROXY_PORT, "HTTP代理"),
        (CLASH_SOCKS_PROXY_PORT, "SOCKS代理")
    ]
    
    available_ports = []
    for port, name in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((CLASH_HOST, port))
            sock.close()
            
            if result == 0:
                debug_log(f"{name}端口 {port} 可用")
                available_ports.append((port, name))
            else:
                debug_log(f"{name}端口 {port} 不可用")
        except Exception as e:
            debug_log(f"检测{name}端口 {port} 时出错: {e}")
    
    if not available_ports:
        log("警告: 未发现可用的Clash代理端口，请检查配置", "WARN")
        log(f"请确认以下端口是否正确：HTTP代理={CLASH_HTTP_PROXY_PORT}, SOCKS代理={CLASH_SOCKS_PROXY_PORT}", "WARN")
    
    return available_ports

def get_public_ip_via_proxy():
    """通过代理获取公网IP（强制代理验证）"""
    # 配置强制使用Clash代理
    proxy_configs = [
        {
            'name': 'HTTP代理',
            'proxies': {
                'http': f'http://{CLASH_HOST}:{CLASH_HTTP_PROXY_PORT}',
                'https': f'http://{CLASH_HOST}:{CLASH_HTTP_PROXY_PORT}'
            }
        },
        {
            'name': 'SOCKS代理',
            'proxies': {
                'http': f'socks5://{CLASH_HOST}:{CLASH_SOCKS_PROXY_PORT}',
                'https': f'socks5://{CLASH_HOST}:{CLASH_SOCKS_PROXY_PORT}'
            }
        }
    ]
    
    for proxy_config in proxy_configs:
        proxy_name = proxy_config['name']
        proxies = proxy_config['proxies']
        
        debug_log(f"尝试通过{proxy_name}获取IP...")
        
        for api_url in PUBLIC_IP_APIS:
            try:
                debug_log(f"使用{proxy_name}访问: {api_url}")
                
                # 创建临时会话，强制使用代理
                temp_session = requests.Session()
                temp_session.trust_env = False  # 不继承系统代理
                temp_session.proxies = proxies
                
                response = temp_session.get(api_url, timeout=PROXY_TEST_TIMEOUT)
                ip = response.text.strip()
                
                # 简单验证IP格式
                if ip and '.' in ip and len(ip.split('.')) == 4:
                    debug_log(f"通过{proxy_name} {api_url} 获取到IP: {ip}")
                    temp_session.close()
                    return ip
                    
            except Exception as e:
                debug_log(f"通过{proxy_name} {api_url} 获取IP失败: {e}")
                continue
    
    return None

def verify_proxy_effectiveness(old_ip):
    """验证代理模式下IP切换是否生效"""
    log("开始验证代理模式下的IP切换效果...")
    
    # 首先检查代理端口是否可用
    available_ports = check_proxy_port_available()
    if not available_ports:
        log("无可用的代理端口，跳过强制代理验证", "WARN")
        return False
    
    for attempt in range(1, MAX_PROXY_RETRY + 1):
        log(f"第 {attempt}/{MAX_PROXY_RETRY} 次验证代理IP...")
        
        # 通过代理获取公网IP
        proxy_ip = get_public_ip_via_proxy()
        
        if proxy_ip:
            if proxy_ip != old_ip:
                log(f"✓ 代理验证成功: 通过代理获取的IP ({proxy_ip}) 已不同于原IP ({old_ip})")
                return True
            else:
                log(f"代理IP ({proxy_ip}) 仍与原IP相同，{PROXY_RETRY_INTERVAL}s后重试...")
        else:
            log(f"无法通过代理获取IP，{PROXY_RETRY_INTERVAL}s后重试...")
        
        if attempt < MAX_PROXY_RETRY:
            time.sleep(PROXY_RETRY_INTERVAL)
    
    log("✗ 代理验证失败: 无法确认代理模式下IP已切换", "ERROR")
    return False

def generate_diagnostic_report():
    """生成诊断报告"""
    log("="*50)
    log("诊断报告")
    log("="*50)
    
    # 统计信息
    total_logs = len(switch_history)
    error_logs = len([h for h in switch_history if h["level"] == "ERROR"])
    warn_logs = len([h for h in switch_history if h["level"] == "WARN"])
    
    log(f"总日志条数: {total_logs}")
    log(f"错误日志: {error_logs}")
    log(f"警告日志: {warn_logs}")
    
    if switch_history:
        duration = (switch_history[-1]["time"] - switch_history[0]["time"]).total_seconds()
        log(f"总耗时: {duration:.1f}s")
    
    # 显示所有错误和警告
    problem_logs = [h for h in switch_history if h["level"] in ["ERROR", "WARN"]]
    if problem_logs:
        log("\n问题日志详情:")
        for log_entry in problem_logs:
            time_str = log_entry["time"].strftime('%H:%M:%S.%f')[:-3]
            log(f"  [{time_str}] [{log_entry['level']}] {log_entry['message']}")
    
    log("="*50)

def main(dry_run=False, debug=False, force=False):
    global debug_mode
    debug_mode = debug

    log("启动IP切换脚本 - 增强诊断版")
    log(f"当前API: {CURRENT_API.upper()} ({'lisahost.com' if CURRENT_API == 'lisa' else 'pqs.pw'})")
    log(f"代理名称: {PROXY_NAME}")
    log(f"SHOW URL: {SHOW_URL}")
    log(f"CHANGE URL: {CHANGE_URL}")
    log(f"调试模式: {'开启' if debug else '关闭'}")
    log(f"干运行模式: {'开启' if dry_run else '关闭'}")
    
    # 环境检测
    proxy_vars = [k for k in os.environ if k.lower().endswith('_proxy')]
    log("环境变量代理检测: " + (", ".join(proxy_vars) if proxy_vars else "无"))
    
    try:
        # 1. 获取当前IP
        if dry_run:
            # 干运行模式：使用固定的模拟IP
            old_ip = "**************"
            log(f"干运行模式：模拟当前IP为 {old_ip}")
        else:
            old_ip = get_current_ip()
        
        # 2. 切换到直连模式
        if not dry_run:
            log("关闭代理 (Clash→DIRECT)…")
            set_clash_mode("direct")
        
        # 3. 调用切换IP接口（带重试机制）
        log("准备调用IP切换接口...")
        returned_ip = None
        ip_change_success = False

        for retry_count in range(1, MAX_IP_CHANGE_RETRIES + 1):
            try:
                log(f"第 {retry_count}/{MAX_IP_CHANGE_RETRIES} 次尝试切换IP...")

                if dry_run:
                    # 干运行模式：模拟IP切换
                    log("干运行模式：模拟IP切换...")
                    import random
                    # 生成一个模拟的新IP
                    returned_ip = f"36.232.{random.randint(1, 255)}.{random.randint(1, 255)}"
                    log(f"模拟返回新IP: {returned_ip}")
                    time.sleep(2)  # 模拟API调用时间
                else:
                    # 真实模式：调用API
                    # 第一次尝试遵循冷却时间，重试时跳过冷却时间，force模式总是跳过
                    skip_cooldown = (retry_count > 1) or force
                    returned_ip = change_ip(skip_cooldown=skip_cooldown)

                # 检查接口返回的IP是否与原IP不同
                if returned_ip == old_ip:
                    log(f"✗ IP切换接口返回相同IP ({returned_ip})，切换失败", "WARN")
                    if retry_count < MAX_IP_CHANGE_RETRIES:
                        log(f"等待 {IP_CHANGE_RETRY_INTERVAL}s 后重试...")
                        time.sleep(IP_CHANGE_RETRY_INTERVAL)
                        continue
                    else:
                        log("已达到最大重试次数，IP切换失败", "ERROR")
                        ip_change_success = False
                        break
                else:
                    log(f"✓ IP切换成功: {old_ip} → {returned_ip}")
                    ip_change_success = True
                    break

            except Exception as e:
                log(f"第 {retry_count} 次IP切换失败: {e}", "ERROR")
                if retry_count < MAX_IP_CHANGE_RETRIES:
                    log(f"等待 {IP_CHANGE_RETRY_INTERVAL}s 后重试...")
                    time.sleep(IP_CHANGE_RETRY_INTERVAL)
                    continue
                else:
                    log("已达到最大重试次数，IP切换失败", "ERROR")
                    if not dry_run:
                        log("恢复代理模式...")
                        set_clash_mode("rule")
                    generate_diagnostic_report()
                    sys.exit(1)

        # 4. 处理IP切换结果
        if not ip_change_success:
            log("IP切换最终失败", "ERROR")
            success = False
        else:
            log(f"✓ IP切换接口返回新IP ({returned_ip})，尝试快速配置替换...")

            # 5. 尝试快速IP替换（新功能）
            fast_replacement_success = False
            if not dry_run:
                fast_replacement_success = fast_replace_proxy_ip(returned_ip)

            if fast_replacement_success:
                log("🚀 快速IP替换成功，跳过传统轮询验证")
                success = True
            else:
                log("快速IP替换失败或已禁用，使用传统轮询验证方式")

                # 6. 等待生效
                log(f"等待 {PROPAGATE_WAIT}s 让IP切换生效...")
                time.sleep(PROPAGATE_WAIT)

                # 7. 轮询检查新IP
                log("开始轮询检查新IP...")
                success = False
                for i in range(1, MAX_POLL + 1):
                    try:
                        if dry_run:
                            # 干运行模式：模拟轮询成功
                            current_ip = returned_ip
                            log(f"干运行模式：模拟检测到IP {current_ip}")
                        else:
                            current_ip = get_current_ip()

                        if current_ip != old_ip:
                            log(f"✓ 第{i}次轮询: 检测到新IP {current_ip}")
                            success = True
                            break
                        else:
                            log(f"第{i}/{MAX_POLL} 次轮询: IP仍为 {old_ip}，{POLL_INTERVAL}s 后重试...")
                            time.sleep(POLL_INTERVAL)
                    except Exception as e:
                        log(f"第{i}次轮询失败: {e}", "ERROR")
                        time.sleep(POLL_INTERVAL)
        
        if not success:
            log("⚠️ 轮询结束，IP仍未变化", "WARN")
            
            # 进行详细的有效性检查
            effectiveness = check_ip_change_effectiveness(old_ip, returned_ip)
            
            if not dry_run:
                log("恢复代理模式...")
                set_clash_mode("rule")
            
            generate_diagnostic_report()
            
            if not effectiveness:
                log("IP切换失败，退出程序", "ERROR")
                sys.exit(1)
        
        # 6.2. 恢复代理模式
        if not dry_run:
            if not fast_replacement_success:
                # 传统方式需要手动恢复代理模式
                log("刷新 Clash 连接 & DNS...")
                flush_dns_and_conn()
                set_clash_mode("rule")
            else:
                # 快速替换已经处理了配置重载，只需要清理连接
                log("清理 Clash 连接...")
                clash_api("/connections", "DELETE")
        
        log("✓ IP切换流程完成")


        #6.5，延时0秒等代理切换成功
        time.sleep(0)
        log("等0秒后开始最终验证")

        # 7. 最终验证：确保代理模式下IP切换生效
        if not dry_run:
            log("开始最终验证步骤...")
            proxy_success = verify_proxy_effectiveness(old_ip)
            
            if proxy_success:
                log("🎉 完整的IP切换流程成功完成！代理模式下IP已更新")
                time.sleep(3)
                log("等3秒后开始最终验证")

            else:
                log("⚠️ IP切换流程完成，但代理验证失败", "WARN")
                # 不退出程序，因为基本的IP切换已经成功
        else:
            log("干运行模式，跳过代理验证步骤")
        
        generate_diagnostic_report()
        
    except KeyboardInterrupt:
        log("用户中断操作", "WARN")
        if not dry_run:
            try:
                set_clash_mode("rule")
                log("已恢复代理模式")
            except:
                pass
        generate_diagnostic_report()
        sys.exit(1)
    except Exception as e:
        log(f"脚本执行异常: {e}", "ERROR")
        if not dry_run:
            try:
                set_clash_mode("rule")
                log("已恢复代理模式")
            except:
                pass
        generate_diagnostic_report()
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="切换家宽 IP 并重载 Clash - 增强诊断版")
    parser.add_argument("--dry-run", action="store_true", help="只调 API，不动 Clash")
    parser.add_argument("--debug", action="store_true", help="启用详细调试输出")
    parser.add_argument("--force", action="store_true", help="强制切换，跳过冷却时间")
    args = parser.parse_args()

    main(dry_run=args.dry_run, debug=args.debug, force=args.force)
