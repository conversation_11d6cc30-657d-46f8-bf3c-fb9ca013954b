#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动打开绑卡链接 GUI 版本
使用 PyQt5 创建图形界面，可以设置打开链接数量和延时
"""

import sys
import webbrowser
import time
import os
import threading
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QLineEdit,
                             QTextEdit, QMessageBox, QGroupBox, QProgressBar)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont

class LinkOpenerThread(QThread):
    """
    在后台线程中打开链接，避免界面卡顿
    """
    progress_updated = pyqtSignal(int)  # 进度更新信号
    log_updated = pyqtSignal(str)       # 日志更新信号
    finished_signal = pyqtSignal()      # 完成信号
    
    def __init__(self, links_data, num_to_open, delay):
        super().__init__()
        self.links_data = links_data
        self.num_to_open = num_to_open
        self.delay = delay
        self.is_running = True
    
    def run(self):
        """
        在后台线程中执行打开链接的操作
        """
        if not self.links_data:
            self.log_updated.emit("没有可用的链接数据！")
            self.finished_signal.emit()
            return
        
        # 确保不超过可用链接数量
        actual_num = min(self.num_to_open, len(self.links_data))
        
        self.log_updated.emit(f"准备打开 {actual_num} 个链接...")
        self.log_updated.emit("-" * 50)
        
        for i in range(actual_num):
            if not self.is_running:  # 检查是否被停止
                break
                
            email, link = self.links_data[i]
            self.log_updated.emit(f"正在打开第 {i+1} 个链接...")
            self.log_updated.emit(f"邮箱: {email}")
            self.log_updated.emit(f"链接: {link[:50]}..." if len(link) > 50 else f"链接: {link}")
            
            try:
                # 在默认浏览器中打开链接
                webbrowser.open(link)
                self.log_updated.emit(f"✓ 成功打开第 {i+1} 个链接")
            except Exception as e:
                self.log_updated.emit(f"✗ 打开第 {i+1} 个链接失败：{e}")
            
            # 更新进度
            progress = int((i + 1) / actual_num * 100)
            self.progress_updated.emit(progress)
            
            # 在打开下一个链接前稍作延迟
            if i < actual_num - 1 and self.is_running:
                self.log_updated.emit(f"等待 {self.delay} 秒后打开下一个链接...")
                # 分割延迟时间，以便能够及时响应停止信号
                delay_steps = int(self.delay * 10)  # 每0.1秒检查一次
                for _ in range(delay_steps):
                    if not self.is_running:
                        break
                    time.sleep(0.1)
            
            self.log_updated.emit("-" * 30)
        
        if self.is_running:
            self.log_updated.emit(f"完成！共打开了 {actual_num} 个链接")
        else:
            self.log_updated.emit("操作已被用户停止")
        
        self.finished_signal.emit()
    
    def stop(self):
        """
        停止线程执行
        """
        self.is_running = False

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.links_data = []
        self.worker_thread = None
        self.init_ui()
        self.load_links_data()
    
    def init_ui(self):
        """
        初始化用户界面
        """
        self.setWindowTitle("自动打开绑卡链接工具")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 设置参数区域
        settings_group = QGroupBox("设置参数")
        settings_layout = QVBoxLayout(settings_group)
        
        # 链接数量设置
        links_layout = QHBoxLayout()
        links_label = QLabel("打开链接数量:")
        links_label.setFont(QFont("Arial", 12))
        self.links_input = QLineEdit()
        self.links_input.setText("2")
        self.links_input.setFont(QFont("Arial", 14))
        self.links_input.setMinimumHeight(35)
        self.links_input.setMaximumWidth(100)
        self.links_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        links_layout.addWidget(links_label)
        links_layout.addWidget(self.links_input)
        links_layout.addStretch()

        # 延时设置
        delay_layout = QHBoxLayout()
        delay_label = QLabel("链接间延时(秒):")
        delay_label.setFont(QFont("Arial", 12))
        self.delay_input = QLineEdit()
        self.delay_input.setText("1.0")
        self.delay_input.setFont(QFont("Arial", 14))
        self.delay_input.setMinimumHeight(35)
        self.delay_input.setMaximumWidth(100)
        self.delay_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        delay_layout.addWidget(delay_label)
        delay_layout.addWidget(self.delay_input)
        delay_layout.addStretch()
        
        settings_layout.addLayout(links_layout)
        settings_layout.addLayout(delay_layout)
        
        # 快捷按钮区域
        quick_buttons_group = QGroupBox("快捷操作")
        quick_buttons_layout = QHBoxLayout(quick_buttons_group)

        self.quick_7_button = QPushButton("打开 7 个链接")
        self.quick_7_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.quick_7_button.setMinimumHeight(45)
        self.quick_7_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.quick_7_button.clicked.connect(lambda: self.start_opening_links_quick(7))

        self.quick_5_button = QPushButton("打开 5 个链接")
        self.quick_5_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.quick_5_button.setMinimumHeight(45)
        self.quick_5_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.quick_5_button.clicked.connect(lambda: self.start_opening_links_quick(5))

        self.close_browser_button = QPushButton("关闭所有浏览器")
        self.close_browser_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.close_browser_button.setMinimumHeight(45)
        self.close_browser_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.close_browser_button.clicked.connect(self.close_all_browsers)

        quick_buttons_layout.addWidget(self.quick_7_button)
        quick_buttons_layout.addWidget(self.quick_5_button)
        quick_buttons_layout.addWidget(self.close_browser_button)

        # 控制按钮
        button_layout = QHBoxLayout()
        self.open_button = QPushButton("开始打开链接")
        self.open_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.open_button.setMinimumHeight(50)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.open_button.clicked.connect(self.start_opening_links)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setFont(QFont("Arial", 12))
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stop_opening_links)
        
        button_layout.addWidget(self.open_button)
        button_layout.addWidget(self.stop_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态信息区域
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Arial", 10))
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.log_text)
        
        # 添加所有组件到主布局
        main_layout.addWidget(settings_group)
        main_layout.addWidget(quick_buttons_group)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(status_group)
        
        # 初始化状态
        self.update_status()
    
    def load_links_data(self):
        """
        加载链接数据
        """
        data_file = "绑卡.txt"
        self.links_data = []
        
        if not os.path.exists(data_file):
            self.log_text.append(f"错误：文件 {data_file} 不存在！")
            return
        
        try:
            with open(data_file, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line:  # 跳过空行
                        continue
                    
                    if '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            email = parts[0].strip()
                            link = parts[1].strip()
                            if email and link:
                                self.links_data.append((email, link))
            
            self.log_text.append(f"成功加载 {len(self.links_data)} 个链接")
            self.update_status()
            
        except Exception as e:
            self.log_text.append(f"读取文件时出错：{e}")
    
    def update_status(self):
        """
        更新状态信息
        """
        if self.links_data:
            self.status_label.setText(f"已加载 {len(self.links_data)} 个链接，就绪")
        else:
            self.status_label.setText("未找到有效链接数据")
    
    def start_opening_links(self):
        """
        开始打开链接
        """
        if not self.links_data:
            QMessageBox.warning(self, "警告", "没有可用的链接数据！\n请检查 绑卡.txt 文件是否存在且格式正确。")
            return

        # 获取输入值并验证
        try:
            num_to_open = int(self.links_input.text())
            if num_to_open < 1:
                raise ValueError("链接数量必须大于0")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的链接数量（正整数）")
            return

        try:
            delay = float(self.delay_input.text())
            if delay < 0:
                raise ValueError("延时必须大于等于0")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的延时时间（数字）")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.open_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 清空日志
        self.log_text.clear()
        
        # 创建并启动工作线程
        self.worker_thread = LinkOpenerThread(self.links_data, num_to_open, delay)
        self.worker_thread.progress_updated.connect(self.progress_bar.setValue)
        self.worker_thread.log_updated.connect(self.log_text.append)
        self.worker_thread.finished_signal.connect(self.on_finished)
        self.worker_thread.start()

    def start_opening_links_quick(self, num_links):
        """
        快捷按钮：直接打开指定数量的链接
        """
        if not self.links_data:
            QMessageBox.warning(self, "警告", "没有可用的链接数据！\n请检查 绑卡.txt 文件是否存在且格式正确。")
            return

        # 使用默认延时
        try:
            delay = float(self.delay_input.text())
            if delay < 0:
                delay = 1.0  # 如果延时输入无效，使用默认值1秒
        except ValueError:
            delay = 1.0  # 如果延时输入无效，使用默认值1秒

        # 禁用所有按钮
        self.open_button.setEnabled(False)
        self.quick_7_button.setEnabled(False)
        self.quick_5_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空日志
        self.log_text.clear()

        # 创建并启动工作线程
        self.worker_thread = LinkOpenerThread(self.links_data, num_links, delay)
        self.worker_thread.progress_updated.connect(self.progress_bar.setValue)
        self.worker_thread.log_updated.connect(self.log_text.append)
        self.worker_thread.finished_signal.connect(self.on_finished)
        self.worker_thread.start()

    def close_all_browsers(self):
        """
        关闭所有浏览器窗口
        """
        try:
            # 关闭Chrome浏览器的所有进程
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True, check=False)

            # 也尝试关闭其他常见浏览器
            browsers = ['msedge.exe', 'firefox.exe', 'opera.exe', 'brave.exe']
            for browser in browsers:
                subprocess.run(['taskkill', '/f', '/im', browser],
                             capture_output=True, text=True, check=False)

            self.log_text.append("✓ 已尝试关闭所有浏览器窗口")

        except Exception as e:
            self.log_text.append(f"✗ 关闭浏览器时出错：{e}")

    def stop_opening_links(self):
        """
        停止打开链接
        """
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.log_text.append("正在停止操作...")
    
    def on_finished(self):
        """
        操作完成后的处理
        """
        # 恢复按钮状态
        self.open_button.setEnabled(True)
        self.quick_7_button.setEnabled(True)
        self.quick_5_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 清理线程
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()
            self.worker_thread = None

def main():
    """
    主函数
    """
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("自动打开绑卡链接工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
